import './register-question.scss';
import React, { ReactElement, useState } from 'react';
import { MnInputText, MnPopin, MnSvg, MnTooltip } from '@mynotary/frontend/shared/ui';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { RegisterPopin } from '../register-popin/register-popin';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestionConfirmationPoppin } from './register-question-confirmation-poppin';

interface RegisterQuestionWithoutActionProps extends MnProps {
  answer: Answer;
  className?: string;
  debounce: boolean;
  disabled: boolean;
  hasRegisterEntryCreatePermission: boolean;
  hasRegisterEntryManuallyCreatePermission: boolean;
  isAllowedContract: boolean;
  isRegisterInitialized: boolean;
  onChange?: (value: string | null) => void;
  onCloseConfirmationPoppin: () => void;
  onCloseFocus: () => void;
  onFocus: () => void;
  onResetEntry: () => void;
  openConfirmationPopin: boolean;
  question: RegisterFormQuestion;
}

export const RegisterQuestionWithoutAction = ({
  answer,
  className,
  debounce,
  disabled,
  hasRegisterEntryCreatePermission,
  hasRegisterEntryManuallyCreatePermission,
  isAllowedContract,
  isRegisterInitialized,
  onChange,
  onCloseConfirmationPoppin,
  onCloseFocus,
  onFocus,
  onResetEntry,
  openConfirmationPopin,
  question
}: RegisterQuestionWithoutActionProps): ReactElement => {
  const [callToActionPopin, setCallToActionPopin] = useState(false);

  const getTooltipContent = (): string => {
    if (disabled) {
      return 'Vous ne pouvez pas prendre de numéro sur un contrat validé';
    } else if (!hasRegisterEntryCreatePermission) {
      return `Vous n'avez pas le droit de prendre un numéro de mandat`;
    } else if (!isRegisterInitialized) {
      return 'Vous ne pouvez pas prendre de numéro sans avoir initialisé le registre au préalable. Rendez-vous dans votre espace Paramètres > Registre (transaction ou gestion)';
    } else if (!isAllowedContract) {
      return 'Vous ne pouvez pas prendre de numéro dans ce contrat';
    }
    return '';
  };

  return (
    <div className={classNames('mn-register-question')}>
      <MnInputText
        className={className}
        debounceTime={debounce ? 500 : 0}
        defaultValue={question.default}
        disabled={disabled || !hasRegisterEntryManuallyCreatePermission}
        format={question.uppercase}
        onChange={onChange}
        onFocus={onFocus}
        placeholder={question.placeholder}
        required={!question.optional}
        value={answer?.value}
      />
      <MnTooltip content={getTooltipContent()}>
        <div className='rd-add' onClick={() => setCallToActionPopin(true)}>
          <MnSvg
            className={classNames('rq-add-register-entry-tooltip', { disabled: true })}
            path='/assets/images/pictos/icon/info-light.svg'
            variant='gray500-primary'
          />
          <div className={classNames('rq-add-register-entry', { disabled: true })}>Prendre un numéro</div>
        </div>
      </MnTooltip>
      <RegisterQuestionConfirmationPoppin
        answer={answer}
        onChange={onChange}
        onClose={onCloseConfirmationPoppin}
        onCloseFocus={onCloseFocus}
        onResetEntry={onResetEntry}
        openConfirmationPopin={openConfirmationPopin}
      />
      <MnPopin
        className='register-question-popin'
        onClose={() => setCallToActionPopin(false)}
        opened={callToActionPopin}
      >
        <RegisterPopin />
      </MnPopin>
    </div>
  );
};
